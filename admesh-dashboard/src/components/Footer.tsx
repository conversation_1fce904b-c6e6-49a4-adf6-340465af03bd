"use client";

import Link from "next/link";
import { FaLinkedin } from "react-icons/fa"; // Optional: for LinkedIn icon
import { useState } from "react";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { <PERSON>ie } from "lucide-react";
import {
  getConsentStatus,
  saveConsentPreferences,
  getAllAcceptedPreferences,
  saveConsentPreferencesWithLogging,
  getDataProcessingPurposes,
  type CookiePreferences
} from "@/lib/cookie-consent";

export default function Footer() {
  const [showCookieModal, setShowCookieModal] = useState(false);
  const [preferences, setPreferences] = useState<CookiePreferences>(() => {
    const status = getConsentStatus();
    return status.preferences || {
      necessary: true,
      analytics: false,
      marketing: false,
      functional: false
    };
  });
  const [processingPurposes] = useState(getDataProcessingPurposes());

  const handlePreferenceChange = (key: keyof CookiePreferences, value: boolean) => {
    if (key === "necessary") return; // Cannot disable necessary cookies
    setPreferences(prev => ({ ...prev, [key]: value }));
  };

  const saveCustomPreferences = () => {
    saveConsentPreferencesWithLogging(preferences, 'customize');
    setShowCookieModal(false);
  };

  const acceptAll = () => {
    const allAccepted = getAllAcceptedPreferences();
    saveConsentPreferencesWithLogging(allAccepted, 'accept');
    setPreferences(allAccepted);
    setShowCookieModal(false);
  };

  return (
    <>
      <footer className="border-t bg-gradient-to-b from-slate-50 to-blue-50 py-8 text-sm text-muted-foreground">
        <div className="mx-auto max-w-6xl px-6 flex flex-col sm:flex-row items-center justify-between gap-6">
          {/* Copyright section */}
          <div className="text-center sm:text-left text-gray-700">
            © {new Date().getFullYear()} <span className="font-semibold text-primary">AdMesh</span>. All rights reserved.
          </div>

          {/* Links section */}
          <div className="flex items-center gap-6">
            <a
              href="https://www.linkedin.com/company/admesh"
              target="_blank"
              rel="noopener noreferrer"
              className="hover:text-primary transition-colors duration-300 ease-in-out text-gray-700 flex items-center gap-1"
            >
              <FaLinkedin className="h-4 w-4" /> LinkedIn
            </a>
            <Link
              href="/terms"
              className="hover:text-primary transition-colors duration-300 ease-in-out text-gray-700"
            >
              Terms
            </Link>
            <Link
              href="/privacy"
              className="hover:text-primary transition-colors duration-300 ease-in-out text-gray-700"
            >
              Privacy
            </Link>
            <button
              onClick={() => setShowCookieModal(true)}
              className="hover:text-primary transition-colors duration-300 ease-in-out text-gray-700"
            >
              Cookie Settings
            </button>
            <a
              href="mailto:<EMAIL>"
              className="hover:text-primary transition-colors duration-300 ease-in-out text-gray-700"
            >
              Contact
            </a>
          </div>
        </div>
      </footer>

      {/* Cookie Preferences Modal */}
      <Dialog open={showCookieModal} onOpenChange={setShowCookieModal}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Cookie className="w-5 h-5" />
              Cookie Preferences
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-6">
            <p className="text-sm text-gray-600">
              Manage your cookie preferences. You can change these settings at any time.
            </p>

            <div className="space-y-3">
              {/* Necessary Cookies */}
              <div className="flex items-center justify-between p-3 border rounded-lg bg-gray-50">
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900 text-sm">Necessary</h3>
                  <p className="text-xs text-gray-600 mt-0.5">
                    Essential for website functionality
                  </p>
                </div>
                <Switch
                  checked={true}
                  disabled={true}
                  className="ml-4"
                />
              </div>

              {/* Analytics Cookies */}
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900 text-sm">Analytics</h3>
                  <p className="text-xs text-gray-600 mt-0.5">
                    Help us understand site usage
                  </p>
                </div>
                <Switch
                  checked={preferences.analytics}
                  onCheckedChange={(checked) => handlePreferenceChange("analytics", checked)}
                  className="ml-4"
                />
              </div>

              {/* Functional Cookies */}
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900 text-sm">Functional</h3>
                  <p className="text-xs text-gray-600 mt-0.5">
                    Enhanced features and personalization
                  </p>
                </div>
                <Switch
                  checked={preferences.functional}
                  onCheckedChange={(checked) => handlePreferenceChange("functional", checked)}
                  className="ml-4"
                />
              </div>

              {/* Marketing Cookies */}
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900 text-sm">Marketing</h3>
                  <p className="text-xs text-gray-600 mt-0.5">
                    Personalized ads and content
                  </p>
                </div>
                <Switch
                  checked={preferences.marketing}
                  onCheckedChange={(checked) => handlePreferenceChange("marketing", checked)}
                  className="ml-4"
                />
              </div>
            </div>

            <div className="flex gap-3 pt-4">
              <Button
                onClick={saveCustomPreferences}
                className="flex-1 bg-black hover:bg-gray-800 text-white"
              >
                Save Preferences
              </Button>
              <Button
                onClick={acceptAll}
                variant="outline"
                className="flex-1"
              >
                Accept All
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
