"use client";

import { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Switch } from "@/components/ui/switch";
import { <PERSON><PERSON> } from "lucide-react";
import {
  getConsentStatus,
  saveConsentPreferences,
  getDefaultPreferences,
  getAllAcceptedPreferences,
  initializeConsentSystem,
  saveConsentPreferencesWithLogging,
  getConsentBannerText,
  getDataProcessingPurposes,
  type CookiePreferences as CookiePrefs
} from "@/lib/cookie-consent";

export default function CookiePreferences() {
  const pathname = usePathname();
  const [showBanner, setShowBanner] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [preferences, setPreferences] = useState<CookiePrefs>(getDefaultPreferences());
  const [bannerText, setBannerText] = useState({
    title: 'We use cookies',
    description: 'We use cookies to improve your experience and analyze site usage.',
    acceptText: 'Accept All',
    declineText: 'Decline',
    customizeText: 'Customize'
  });
  const [processingPurposes] = useState(getDataProcessingPurposes());

  // Define landing pages where cookie banner should be shown
  const landingPages = ['/', '/brands', '/agents', '/users'];
  const isLandingPage = landingPages.includes(pathname);
  const isDashboard = pathname.startsWith('/dashboard');

  // Track if component has mounted on the client
  const [hasMounted, setHasMounted] = useState(false);

  // Initialize consent system and check if banner should be shown
  useEffect(() => {
    setHasMounted(true);

    // Initialize the consent system
    const consentStatus = initializeConsentSystem();

    // Load existing preferences if available
    if (consentStatus.preferences) {
      setPreferences(consentStatus.preferences);
    }

    // Load region-specific banner text
    getConsentBannerText().then(setBannerText);

    // Show banner only on landing pages and when consent is needed
    if (isLandingPage && consentStatus.shouldShowBanner) {
      // Show banner after a short delay to prevent flash on initial load
      const timer = setTimeout(() => setShowBanner(true), 1500);
      return () => clearTimeout(timer);
    }
  }, [isLandingPage]);

  const savePreferences = (prefs: CookiePrefs, action: 'accept' | 'decline' | 'customize' = 'customize') => {
    // Use the comprehensive consent utility with compliance logging
    saveConsentPreferencesWithLogging(prefs, action);
    setPreferences(prefs);
    setShowBanner(false);
    setShowModal(false);
  };

  const acceptAll = () => {
    const allAccepted = getAllAcceptedPreferences();
    savePreferences(allAccepted, 'accept');
  };

  const acceptNecessaryOnly = () => {
    const necessaryOnly = getDefaultPreferences();
    savePreferences(necessaryOnly, 'decline');
  };

  const openPreferencesModal = () => {
    setShowModal(true);
    setShowBanner(false);
  };

  const handlePreferenceChange = (key: keyof CookiePrefs, value: boolean) => {
    if (key === "necessary") return; // Cannot disable necessary cookies
    setPreferences(prev => ({ ...prev, [key]: value }));
  };

  const saveCustomPreferences = () => {
    savePreferences(preferences, 'customize');
  };

  // Cookie banner - only show on landing pages
  if (showBanner && isLandingPage) {
    return (
      <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50 max-w-md w-full mx-4">
        <Card className="border border-gray-200 shadow-2xl bg-white">
          <CardContent className="p-6">
            <div className="flex items-start gap-3 mb-4">
              <Cookie className="w-5 h-5 text-gray-700 mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="font-medium text-gray-900 mb-1">{bannerText.title}</h3>
                <p className="text-sm text-gray-600 leading-relaxed">
                  {bannerText.description}
                </p>
              </div>
            </div>
            <div className="flex flex-col sm:flex-row gap-2">
              <Button
                onClick={acceptAll}
                className="flex-1 bg-black hover:bg-gray-800 text-white text-sm h-9"
              >
                {bannerText.acceptText}
              </Button>
              <Button
                onClick={openPreferencesModal}
                variant="outline"
                className="flex-1 text-sm h-9"
              >
                {bannerText.customizeText}
              </Button>
              <Button
                onClick={acceptNecessaryOnly}
                variant="ghost"
                className="flex-1 text-sm h-9"
              >
                {bannerText.declineText}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Only check for consent after component has mounted on the client
  const hasConsent = hasMounted && getConsentStatus().hasConsent;

  return (
    <>
      {/* Minimal cookie settings button - only show if user has consented and not on dashboard */}
      {!showBanner && hasConsent && !isDashboard && (
        <div className="fixed bottom-4 right-4 z-[60]">
          <Button
            onClick={() => setShowModal(true)}
            variant="ghost"
            size="sm"
            className="bg-white/90 backdrop-blur-sm shadow-sm border border-gray-200 hover:bg-white text-xs text-gray-600 hover:text-gray-900 px-3 py-1.5 h-auto"
          >
            Cookie Settings
          </Button>
        </div>
      )}

      <Dialog open={showModal} onOpenChange={setShowModal}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Cookie className="w-5 h-5" />
              Cookie Preferences
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-6">
            <p className="text-sm text-gray-600">
              Manage your cookie preferences. You can change these settings at any time. We are committed to transparency about how we process your data.
            </p>

            <div className="space-y-4">
              {/* Necessary Cookies */}
              <div className="p-4 border rounded-lg bg-gray-50">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium text-gray-900 text-sm">Necessary Cookies</h3>
                  <Switch
                    checked={true}
                    disabled={true}
                    className="ml-4"
                  />
                </div>
                <p className="text-xs text-gray-600 mb-2">
                  Essential for website functionality. These cannot be disabled.
                </p>
                <details className="text-xs text-gray-500">
                  <summary className="cursor-pointer hover:text-gray-700">View purposes</summary>
                  <ul className="mt-1 ml-4 list-disc">
                    {processingPurposes.necessary.map((purpose, index) => (
                      <li key={index}>{purpose}</li>
                    ))}
                  </ul>
                </details>
              </div>

              {/* Analytics Cookies */}
              <div className="p-4 border rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium text-gray-900 text-sm">Analytics Cookies</h3>
                  <Switch
                    checked={preferences.analytics}
                    onCheckedChange={(checked) => handlePreferenceChange("analytics", checked)}
                    className="ml-4"
                  />
                </div>
                <p className="text-xs text-gray-600 mb-2">
                  Help us understand how visitors interact with our website.
                </p>
                <details className="text-xs text-gray-500">
                  <summary className="cursor-pointer hover:text-gray-700">View purposes</summary>
                  <ul className="mt-1 ml-4 list-disc">
                    {processingPurposes.analytics.map((purpose, index) => (
                      <li key={index}>{purpose}</li>
                    ))}
                  </ul>
                </details>
              </div>

              {/* Functional Cookies */}
              <div className="p-4 border rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium text-gray-900 text-sm">Functional Cookies</h3>
                  <Switch
                    checked={preferences.functional}
                    onCheckedChange={(checked) => handlePreferenceChange("functional", checked)}
                    className="ml-4"
                  />
                </div>
                <p className="text-xs text-gray-600 mb-2">
                  Enable enhanced features and personalization.
                </p>
                <details className="text-xs text-gray-500">
                  <summary className="cursor-pointer hover:text-gray-700">View purposes</summary>
                  <ul className="mt-1 ml-4 list-disc">
                    {processingPurposes.functional.map((purpose, index) => (
                      <li key={index}>{purpose}</li>
                    ))}
                  </ul>
                </details>
              </div>

              {/* Marketing Cookies */}
              <div className="p-4 border rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium text-gray-900 text-sm">Marketing Cookies</h3>
                  <Switch
                    checked={preferences.marketing}
                    onCheckedChange={(checked) => handlePreferenceChange("marketing", checked)}
                    className="ml-4"
                  />
                </div>
                <p className="text-xs text-gray-600 mb-2">
                  Used to deliver personalized advertisements and measure their effectiveness.
                </p>
                <details className="text-xs text-gray-500">
                  <summary className="cursor-pointer hover:text-gray-700">View purposes</summary>
                  <ul className="mt-1 ml-4 list-disc">
                    {processingPurposes.marketing.map((purpose, index) => (
                      <li key={index}>{purpose}</li>
                    ))}
                  </ul>
                </details>
              </div>
            </div>

            <div className="text-xs text-gray-500 p-3 bg-gray-50 rounded-lg">
              <p className="mb-1"><strong>Your Rights:</strong></p>
              <p>You have the right to withdraw consent at any time. You can also request access to, correction of, or deletion of your personal data. Contact us at <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline"><EMAIL></a> for data protection inquiries.</p>
            </div>

            <div className="flex gap-3 pt-4">
              <Button
                onClick={saveCustomPreferences}
                className="flex-1 bg-black hover:bg-gray-800 text-white"
              >
                Save Preferences
              </Button>
              <Button
                onClick={acceptAll}
                variant="outline"
                className="flex-1"
              >
                Accept All
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
