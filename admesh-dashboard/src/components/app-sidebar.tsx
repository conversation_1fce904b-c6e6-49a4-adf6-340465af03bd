"use client";

import {
  LayoutList,
  Settings,
  Package,
  BarChart2,
  DollarSign,
  BadgeDollarSign,
  LogOut,
  Moon,
  Sun,
  History,
  MessageCircle,
  ChevronLeft,
  ChevronRight,
  Menu,
  Award,
  Trophy,
  Users,
  Building,
  ClipboardList,
  Key,
  FileText,
  Search,
} from "lucide-react";
import { usePathname, useRouter } from "next/navigation";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { useAuth } from "@/hooks/use-auth";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { useTheme } from "next-themes";
import { signOut } from "firebase/auth";
import { auth } from "@/lib/firebase";
import { useState, useEffect } from "react";
import { CreditsDisplay } from "@/components/credits-display";
import XpDisplay from "@/components/XpDisplay";
import UserBadges from "@/components/UserBadges";
import { useBadges } from "@/contexts/badge-context";

// Define the type for navigation items
interface NavItem {
  title: string;
  url: string;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  beta?: boolean;
}

const navItemsByRole: Record<string, NavItem[]> = {
  user: [
    { title: "Chat", url: "/dashboard/user/chat", icon: MessageCircle },
    { title: "History", url: "/dashboard/user/history", icon: History },
    // { title: "Automation", url: "/dashboard/user/automation", icon: Sparkles, beta: true },
    { title: "Badges & XP", url: "/dashboard/user/badges", icon: Award },
    { title: "Leaderboard", url: "/leaderboard", icon: Trophy },
    { title: "Payments", url: "/dashboard/user/payments", icon: DollarSign },
    { title: "Profile", url: "/dashboard/user/profile", icon: Settings },
  ],
  agent: [
    { title: "Queries", url: "/dashboard/agent/queries", icon: LayoutList },
    { title: "Earnings", url: "/dashboard/agent/earnings", icon: BadgeDollarSign },
    { title: "Conversions", url: "/dashboard/agent/conversions", icon: BarChart2 },
    { title: "API Keys", url: "/dashboard/agent/api-keys", icon: Key },
    { title: "Profile", url: "/dashboard/agent/profile", icon: Settings },
    { title: "Documentation", url: "/dashboard/agent/docs", icon: FileText },
  ],
  brand: [
    { title: "Offers", url: "/dashboard/brand/offers", icon: LayoutList },
    { title: "Products", url: "/dashboard/brand/products", icon: Package },
    { title: "GEO Check", url: "/dashboard/brand/geo-check", icon: Search, beta: true },
    { title: "Billing", url: "/dashboard/brand/billing", icon: DollarSign },
  ],
  admin: [
    { title: "Products", url: "/dashboard/admin/products", icon: Package },
    { title: "Users", url: "/dashboard/admin/users", icon: Users },
    { title: "Brands", url: "/dashboard/admin/brands", icon: Building },
    { title: "Waitlist", url: "/dashboard/admin/waitlist", icon: ClipboardList },
    // { title: "Settings", url: "/dashboard/admin/settings", icon: Settings },
  ],
};

export function AppSidebar() {
  const { user, role } = useAuth();
  const pathname = usePathname();
  const router = useRouter();
  const items = role ? navItemsByRole[role as keyof typeof navItemsByRole] || [] : [] as NavItem[];
  const { theme, setTheme } = useTheme();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const { unreadBadges, markBadgesAsRead } = useBadges();

  // Check if we're on mobile based on screen width
  useEffect(() => {
    // Only run on client side
    if (typeof window !== 'undefined') {
      const checkIfMobile = () => {
        setIsMobile(window.innerWidth < 768);
        if (window.innerWidth < 768) {
          setIsCollapsed(true);
        }
      };

      // Initial check
      checkIfMobile();

      // Add event listener for resize
      window.addEventListener('resize', checkIfMobile);

      // Clean up
      return () => window.removeEventListener('resize', checkIfMobile);
    }
  }, []);

  const NavigationItems = () => (
    <nav className="space-y-1 py-2 w-full">
      {items.map((item) => {
        // Check if this is the Badges & XP link and we have unread badges
        const hasBadgeNotification = item.title === "Badges & XP" && unreadBadges > 0;

        // Handle click on Badges & XP link to mark badges as read
        const handleClick = () => {
          if (item.title === "Badges & XP") {
            markBadgesAsRead();
          }
          if (isMobile) {
            setIsOpen(false);
          }
        };

        return (
          <Link
            key={item.title}
            href={item.url}
            onClick={handleClick}
            className={cn(
              "group relative flex items-center px-3 py-3 rounded-lg transition-all duration-200 text-sm font-medium w-full",
              isCollapsed && !isMobile ? "justify-center" : "gap-3",
              pathname === item.url
                ? "bg-primary text-primary-foreground shadow-sm"
                : "text-foreground hover:bg-accent hover:text-accent-foreground"
            )}
          >
            <div className="relative">
              <item.icon
                className={cn(
                  "h-5 w-5 flex-shrink-0 transition-colors duration-200",
                  isCollapsed && !isMobile ? "mx-auto" : "",
                  pathname === item.url ? "text-primary-foreground" : "text-muted-foreground group-hover:text-accent-foreground"
                )}
              />
              {hasBadgeNotification && (
                <span className="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-[10px] font-medium text-white">
                  {unreadBadges}
                </span>
              )}
            </div>
            <div className={cn("flex items-center justify-between w-full", isCollapsed && !isMobile && "hidden")}>
              <span className="truncate whitespace-nowrap font-medium">{item.title}</span>
              <div className="flex items-center gap-2">
                {item.beta && (
                  <span className="text-[10px] font-semibold px-2 py-1 rounded-full bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400 border border-blue-200 dark:border-blue-800">
                    BETA
                  </span>
                )}
                {hasBadgeNotification && (
                  <span className="flex h-5 min-w-5 items-center justify-center rounded-full bg-red-500 text-[10px] font-medium text-white px-1">
                    {unreadBadges}
                  </span>
                )}
              </div>
            </div>
          </Link>
        );
      })}
    </nav>
  );

  const UserSection = () => (
    <div className="mt-auto w-full">

      {user && (!isCollapsed || isMobile) && (
        <div className="flex items-center gap-3 mb-6 p-3 rounded-lg bg-card border border-border">
          <Link
            href={role === "user" ? "/dashboard/user/profile" : role === "agent" ? "/dashboard/agent/profile" : "#"}
            className={cn(role !== "user" && role !== "agent" && "pointer-events-none")}
          >
            <Avatar className="h-10 w-10 cursor-pointer hover:ring-2 hover:ring-ring transition-all">
              <AvatarImage
                src={user.photoURL || ""}
                alt={user.displayName || "User"}
              />
              <AvatarFallback className="bg-primary text-primary-foreground font-semibold">
                {user.displayName?.[0] || "U"}
              </AvatarFallback>
            </Avatar>
          </Link>
          <div className="flex flex-col min-w-0 flex-1">
            <div className="text-sm font-semibold text-card-foreground truncate">
              {user.displayName || user.email}
            </div>
            {role === "user" && (
              <>
                <div className="text-xs flex items-center gap-2">
                  <CreditsDisplay />
                  <span className="text-muted-foreground">|</span>
                  <XpDisplay />
                </div>
                <div className="mt-1">
                  <UserBadges size="sm" showTooltip={true} maxDisplay={3} showShareButton={true} />
                </div>
              </>
            )}
          </div>
        </div>
      )}

      {/* Theme Toggle */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setTheme(theme === "light" ? "dark" : "light")}
        className={cn(
          "w-full mb-3 px-3 py-2 rounded-lg text-foreground hover:bg-accent hover:text-accent-foreground transition-colors duration-200",
          isCollapsed && !isMobile ? "justify-center" : "justify-start gap-3"
        )}
      >
        {theme === "dark" ? (
          <Sun className="h-4 w-4 text-muted-foreground" />
        ) : (
          <Moon className="h-4 w-4 text-muted-foreground" />
        )}
        <span className={cn("font-medium", isCollapsed && !isMobile && "hidden")}>
          {theme === "dark" ? "Light Mode" : "Dark Mode"}
        </span>
      </Button>

      {/* Logout */}
      <Button
        variant="ghost"
        size="sm"
        onClick={async () => {
          await signOut(auth);
          router.push("/");
          if (isMobile) setIsOpen(false);
        }}
        className={cn(
          "w-full text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-950/20 px-3 py-2 rounded-lg transition-colors duration-200",
          isCollapsed && !isMobile ? "justify-center" : "justify-start gap-3"
        )}
      >
        <LogOut className="h-4 w-4" />
        <span className={cn("font-medium", isCollapsed && !isMobile && "hidden")}>
          Logout
        </span>
      </Button>
    </div>
  );

  // Mobile sidebar with Sheet - completely off-screen when closed
  if (isMobile) {
    return (
      <>
        <header className="h-15 border-b border-border flex items-center top-0 bg-background z-10">
          <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <Menu className="h-5 w-5" />
                <span className="sr-only">Toggle menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="p-0 w-64 sm:w-72 bg-background">
              <div className="flex flex-col h-full">
                <div className="p-6 border-b border-border bg-muted/30">
                  <div className="flex items-center justify-between">
                    {(!isCollapsed || isMobile) && ( // Show "AdMesh" label when expanded
                      <Link
                        href="/"
                        className="flex items-center gap-3 group"
                        onClick={() => setIsOpen(false)}
                      >
                        <div className="flex items-center justify-center w-8 h-8 bg-primary text-primary-foreground rounded-lg group-hover:scale-105 transition-transform">
                          <span className="font-bold text-sm">A</span>
                        </div>
                        <div className="flex flex-col">
                          <span className="text-xl font-bold text-foreground tracking-tight">
                            AdMesh
                          </span>
                          <span className="text-[10px] font-medium px-1.5 py-0.5 rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 -mt-1">
                            BETA
                          </span>
                        </div>
                      </Link>
                    )}
                    {/* <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => setIsOpen(false)}
                    >
                      <X className="h-4 w-4" />
                      <span className="sr-only">Close</span>
                    </Button> */}
                  </div>
                </div>

                <div className="flex-1 overflow-auto p-4">
                  <NavigationItems />
                </div>

                <div className="border-t border-border p-4 bg-muted/30">
                  <UserSection />
                </div>
              </div>
            </SheetContent>
          </Sheet>
          {!isCollapsed && ( // Hide "AdMesh" label when collapsed
            <div className="flex-1 flex items-center justify-center">
              <div className="flex items-center gap-3">
                <div className="flex items-center justify-center w-8 h-8 bg-primary text-primary-foreground rounded-lg">
                  <span className="font-bold text-sm">A</span>
                </div>
                <div className="flex flex-col">
                  <span className="text-xl font-bold text-foreground tracking-tight">
                    AdMesh
                  </span>
                  <span className="text-[10px] font-medium px-1.5 py-0.5 rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 -mt-1">
                    BETA
                  </span>
                </div>
              </div>
            </div>
          )}
        </header>
      </>
    );
  }

  // Desktop sidebar - only shown at md breakpoint and above
  return (
    <aside
      className={cn(
        "hidden md:flex flex-col h-screen sticky top-0 border-r border-border transition-all duration-300 bg-background shadow-sm",
        isCollapsed ? "w-16" : "w-64"
      )}
    >
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="p-6 border-b border-border flex items-center justify-between bg-muted/30">
          {!isCollapsed && (
            <Link href="/" className="flex items-center gap-3 group focus:outline-none">
              <div className="flex items-center justify-center w-8 h-8 bg-primary text-primary-foreground rounded-lg group-hover:scale-105 transition-transform">
                <span className="font-bold text-sm">A</span>
              </div>
              <div className="flex flex-col">
                <span className="text-xl font-bold text-foreground tracking-tight">
                  AdMesh
                </span>
                <span className="text-[10px] font-medium px-1.5 py-0.5 rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 -mt-1">
                  BETA
                </span>
              </div>
            </Link>
          )}
          {isCollapsed && (
            <div className="flex items-center justify-center w-8 h-8 bg-primary text-primary-foreground rounded-lg mx-auto">
              <span className="font-bold text-sm">A</span>
            </div>
          )}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsCollapsed(!isCollapsed)}
            className={cn(isCollapsed ? "mx-auto" : "ml-auto")}
            aria-label={isCollapsed ? "Expand" : "Collapse"}
          >
            {isCollapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
          </Button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-4">
          <NavigationItems />
        </div>

        {/* Footer */}
        <div className="border-t border-border p-4 bg-muted/30">
          <UserSection />
        </div>
      </div>
    </aside>
  );
}