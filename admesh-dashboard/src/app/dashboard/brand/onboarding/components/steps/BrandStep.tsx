"use client";

import { useState, useEffect } from "react";
import { toast } from "sonner";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2, ArrowRight, Mail, CheckCircle, AlertCircle } from "lucide-react";
import { sendEmailVerification } from "firebase/auth";
import { useAuth } from "@/hooks/use-auth";
import { validateEmailDomainMatch, getDomainValidationError } from "@/lib/utils";
import FormField from "../ui/FormField";
import FormSection from "../ui/FormSection";
import AnimatedContainer from "../ui/AnimatedContainer";
import { Brand } from "@/types/onboarding";


interface BrandStepProps {
  brand: {
    website: string;
    brand_name: string;
    logo_url: string;
    work_email: string;
    headquarters: string;
    application_type: string;
  };
  setBrand: (brand: Brand) => void;
  errors: Record<string, string>;
  onNext: () => void;
  loading: boolean;
  userEmail: string;
  onFetchInfo: (website: string) => Promise<boolean>;
}

const BrandStep = ({ brand, setBrand, errors, onNext, loading, userEmail, onFetchInfo }: BrandStepProps) => {
  const { user, refreshUser } = useAuth();
  const [showAllFields, setShowAllFields] = useState(false);
  const [fetchingDomainInfo, setFetchingDomainInfo] = useState(false);
  const [domainFetchError, setDomainFetchError] = useState("");
  const [sendingVerification, setSendingVerification] = useState(false);
  const [emailVerificationSent, setEmailVerificationSent] = useState(false);


  // Check if brand data is pre-filled (indicating auto-fill from existing data)
  const isDataPreFilled = brand.website || brand.brand_name || brand.work_email;

  // Auto-show fields if data is pre-filled
  useEffect(() => {
    if (isDataPreFilled && !showAllFields) {
      console.log("BrandStep: Auto-showing fields due to pre-filled data");
      setShowAllFields(true);
    }
  }, [isDataPreFilled, showAllFields]);

  // Check if user email domain matches brand domain
  const emailMatchesDomain = validateEmailDomainMatch(userEmail, brand.website);
  const domainValidationError = getDomainValidationError(userEmail, brand.website);

  // Check if email verification is required and if user's email is verified
  const requiresEmailVerification = brand.website && !emailMatchesDomain;
  const isEmailVerified = user?.emailVerified || false;

  const handleSendEmailVerification = async () => {
    if (!user) {
      toast.error("User not found");
      return;
    }

    // Check domain validation first
    if (domainValidationError) {
      toast.error(domainValidationError);
      return;
    }

    setSendingVerification(true);
    try {
      await sendEmailVerification(user);
      setEmailVerificationSent(true);
      toast.success(
        "Verification email sent! Please check your inbox and click the link to verify your email.",
        {
          action: {
            label: "Refresh Status",
            onClick: () => {
              refreshUser();
              // Check verification status after a short delay
              setTimeout(() => {
                refreshUser();
              }, 2000);
            }
          },
          duration: 10000
        }
      );
    } catch (error) {
      console.error("Error sending verification email:", error);
      toast.error("Failed to send verification email. Please try again later.");
    } finally {
      setSendingVerification(false);
    }
  };

  const handleFetchInfo = async () => {
    if (!brand.website) {
      toast.error("Please enter a website");
      return;
    }
    setFetchingDomainInfo(true);
    setDomainFetchError("");

    try {
      // Use the parent component's fetchWebsiteInfo function
      const success = await onFetchInfo(brand.website);

      if (success) {
        setShowAllFields(true);
      } else {
        setDomainFetchError("Could not fetch information for this website. Please fill in the details manually.");
        setShowAllFields(true);
      }
    } catch (err) {
      console.error("❌ Error fetching website info:", err);
      setDomainFetchError("Could not fetch information for this website. Please fill in the details manually.");
      setShowAllFields(true);
    } finally {
      setFetchingDomainInfo(false);
    }
  };

  const handleContinue = async () => {
    // If fields aren't shown yet, show them first
    if (!showAllFields) {
      setShowAllFields(true);
      return; // Don't proceed with validation yet
    }

    // Check if email verification is required but not completed
    if (requiresEmailVerification && !isEmailVerified) {
      toast.error("Please verify your email before continuing. Check your inbox for the verification link.");
      return;
    }

    // Check domain validation
    if (domainValidationError) {
      toast.error(domainValidationError);
      return;
    }

    onNext();
  };

  return (
    <AnimatedContainer dataStep={1}>
      <FormSection
        title="Let's start with your brand"
        description="Tell us about who you are and how we can reach you."
      >
        <div className="grid gap-6 w-full">
          <FormField>
            <Label htmlFor="website" className="text-sm font-medium">Website</Label>
            <div className="flex flex-col sm:flex-row gap-2">
              <div className="w-full">
                <Input
                  id="website"
                  placeholder="yourbrand.com"
                  value={brand.website}
                  onChange={(e) => setBrand({ ...brand, website: e.target.value })}
                  className={`transition-all duration-200 focus:ring-2 focus:ring-primary/20 ${errors.website ? 'border-red-500' : ''} ${isDataPreFilled && brand.website ? 'bg-green-50 border-green-200' : ''}`}
                />
                {errors.website && (
                  <p className="text-xs text-red-500 mt-1">{errors.website}</p>
                )}
                {isDataPreFilled && brand.website && (
                  <p className="text-xs text-green-600 mt-1">✓ Auto-filled from your account</p>
                )}
              </div>
              <Button
                onClick={handleFetchInfo}
                disabled={fetchingDomainInfo || loading}
                variant="outline"
                className="whitespace-nowrap w-full sm:w-auto"
              >
                {fetchingDomainInfo || loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {loading ? "Auto-fetching..." : "Fetching..."}
                  </>
                ) : (
                  <>
                    <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M12 7V12L15 15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    Fetch Info
                  </>
                )}
              </Button>
            </div>
            {domainFetchError && (
              <p className="text-xs text-red-500 mt-1">{domainFetchError}</p>
            )}
          </FormField>

          {/* Email Verification Section */}
          {brand.website && (
            <FormField>
              <Label className="text-sm font-medium">Email Verification</Label>
              <div className="space-y-3">
                <div className="flex items-center gap-2 p-3 rounded-lg border bg-muted/30">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">Your email:</span>
                  <span className="text-sm font-medium">{userEmail}</span>
                  {isEmailVerified ? (
                    <CheckCircle className="h-4 w-4 text-green-500 ml-auto" />
                  ) : (
                    <AlertCircle className="h-4 w-4 text-orange-500 ml-auto" />
                  )}
                </div>

                {emailMatchesDomain ? (
                  <div className="flex items-center gap-2 text-sm text-green-600 dark:text-green-400">
                    <CheckCircle className="h-4 w-4" />
                    <span>Email domain matches your website domain</span>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm text-orange-600 dark:text-orange-400">
                      <AlertCircle className="h-4 w-4" />
                      <span>Email verification required for domain validation</span>
                    </div>
                    {domainValidationError && (
                      <p className="text-xs text-red-500">{domainValidationError}</p>
                    )}
                    {!isEmailVerified && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={handleSendEmailVerification}
                        disabled={sendingVerification || !!domainValidationError}
                        className="w-full sm:w-auto"
                      >
                        {sendingVerification ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Sending...
                          </>
                        ) : (
                          <>
                            <Mail className="mr-2 h-4 w-4" />
                            {emailVerificationSent ? "Resend Verification Email" : "Send Verification Email"}
                          </>
                        )}
                      </Button>
                    )}
                    {isEmailVerified && (
                      <div className="flex items-center gap-2 text-sm text-green-600 dark:text-green-400">
                        <CheckCircle className="h-4 w-4" />
                        <span>Email verified successfully</span>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </FormField>
          )}

          {showAllFields && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              transition={{ duration: 0.3 }}
              className="space-y-6"
            >
              <FormField>
                <Label htmlFor="brand_name" className="text-sm font-medium">Brand Name</Label>
                <Input
                  id="brand_name"
                  placeholder="Your Brand Name"
                  value={brand.brand_name}
                  onChange={(e) => setBrand({ ...brand, brand_name: e.target.value })}
                  className={`transition-all duration-200 focus:ring-2 focus:ring-primary/20 ${errors.brand_name ? 'border-red-500' : ''} ${isDataPreFilled && brand.brand_name ? 'bg-green-50 border-green-200' : ''}`}
                />
                {errors.brand_name && (
                  <p className="text-xs text-red-500 mt-1">{errors.brand_name}</p>
                )}
                {isDataPreFilled && brand.brand_name && (
                  <p className="text-xs text-green-600 mt-1">✓ Auto-filled from website information</p>
                )}
              </FormField>

              <FormField>
                <Label htmlFor="application_type" className="text-sm font-medium">Application Type</Label>
                <select
                  id="application_type"
                  value={brand.application_type}
                  onChange={(e) => setBrand({ ...brand, application_type: e.target.value })}
                  className={`w-full rounded-md border border-input bg-background p-2 focus:ring-2 focus:ring-primary/20 ${errors.application_type ? 'border-red-500' : ''}`}
                >
                  <option value="website">Website</option>
                  <option value="mobile_app">Mobile App</option>
                  <option value="desktop">Desktop Application</option>
                  <option value="both">Web & Mobile</option>
                  <option value="other">Other</option>
                </select>
                {errors.application_type ? (
                  <p className="text-xs text-red-500 mt-1">{errors.application_type}</p>
                ) : (
                  <p className="text-xs text-muted-foreground mt-1">
                    Select the platform where your product is available.
                  </p>
                )}
              </FormField>

              {/* Show work_email input ONLY if user's email doesn't match brand domain */}
              {!emailMatchesDomain && (
                <FormField>
                  <Label htmlFor="work_email" className="text-sm font-medium">Work Email</Label>
                  <div className="flex items-center w-full">
                    <Input
                      id="work_email_prefix"
                      type="text"
                      placeholder="you"
                      value={brand.work_email.split('@')[0] || ''}
                      onChange={(e) => {
                        const emailPrefix = e.target.value;
                        const cleanDomain = brand.website.replace(/^https?:\/\//, "").replace(/^www\./, "").split('/')[0];
                        setBrand({ ...brand, work_email: `${emailPrefix}@${cleanDomain}` });
                      }}
                      className={`w-2/5 sm:w-1/2 rounded-r-none transition-all duration-200 focus:ring-2 focus:ring-primary/20 ${errors.work_email ? 'border-red-500' : ''}`}
                    />
                    <div className="w-3/5 sm:w-1/2 flex items-center bg-muted px-2 sm:px-3 py-2 border border-l-0 border-input rounded-r-md text-muted-foreground overflow-hidden text-ellipsis whitespace-nowrap text-xs sm:text-sm">
                      @{brand.website.replace(/^https?:\/\//, "").replace(/^www\./, "").split('/')[0]}
                    </div>
                  </div>
                  {errors.work_email ? (
                    <p className="text-xs text-red-500 mt-1">{errors.work_email}</p>
                  ) : (
                    <p className="text-xs text-muted-foreground mt-1">
                      We&apos;ll use this to verify your ownership of the domain.
                    </p>
                  )}
                </FormField>
              )}
            </motion.div>
          )}

          <motion.div
            className="flex justify-end pt-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6 }}
          >
            <motion.div
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.97 }}
              className="w-full sm:w-auto"
            >
              <Button
                onClick={handleContinue}
                disabled={loading}
                className="w-full sm:w-auto px-6 py-2 rounded-full relative overflow-hidden group shadow-md hover:shadow-lg"
              >
                <div className="relative z-10 flex items-center justify-center">
                  {!showAllFields ? "Continue" : "Next"}
                  <ArrowRight className="ml-2 w-4 h-4 transition-transform duration-300 group-hover:translate-x-1" />
                </div>
                <div className="absolute inset-0 bg-gradient-to-r from-primary to-blue-600 dark:from-primary dark:to-blue-500 opacity-90 group-hover:opacity-100 transition-all duration-300"></div>
              </Button>
            </motion.div>
          </motion.div>
        </div>
      </FormSection>
    </AnimatedContainer>
  );
};

export default BrandStep;
